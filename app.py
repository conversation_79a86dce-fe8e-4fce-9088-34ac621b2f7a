from flask import Flask, jsonify, request, Response, send_file, url_for
from datetime import datetime, timedelta
import random
import os
import json
import requests
from html_export import export_to_word

app = Flask(__name__)
app = Flask(__name__)
# 配置JSON响应不转义中文字符
app.config['JSON_AS_ASCII'] = False

# API 配置
API_BASE_URL = "http://127.0.0.1:5001"
API_TIMEOUT = 30

def json_response(data, status_code=200):
    """自定义JSON响应函数，确保中文字符正确显示"""
    response = Response(
        json.dumps(data, ensure_ascii=False, indent=2),
        status=status_code,
        mimetype='application/json; charset=utf-8'
    )
    return response

def call_external_api(endpoint, timeout=API_TIMEOUT):
    """调用外部API获取数据"""
    try:
        url = f"{API_BASE_URL}{endpoint}"
        response = requests.get(url, timeout=timeout)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"调用API失败 {url}: {e}")
        return None

# 模拟数据生成函数
# def generate_random_change():
#     """生成随机的日变化百分比"""
#     return round(random.uniform(-3.0, 3.0), 2)

def calculate_usage_rate(total, used):
    """计算使用率"""
    if total == 0:
        return 0.0
    return round((used / total) * 100, 2)

def get_health_status(usage_rate, thresholds):
    """根据使用率获取健康状态"""
    if usage_rate < thresholds['green']:
        return "正常"
    elif usage_rate < thresholds['yellow']:
        return "观察"
    else:
        return "警告"

@app.route('/api/storage', methods=['GET'])
def get_storage_capacity():
    """获取存储容量信息"""
    # 调用外部API获取存储数据
    api_data = call_external_api('/api/storage/ratio')

    if api_data is None:
        return json_response({
            "status": "error",
            "message": "无法获取存储容量数据",
            "timestamp": datetime.now().isoformat()
        }, 500)

    # 转换API数据为标准格式
    storage_pools = []
    try:
        # 假设API返回的数据结构，根据实际情况调整
        if isinstance(api_data, dict) and 'data' in api_data:
            pools_data = api_data['data']
        elif isinstance(api_data, list):
            pools_data = api_data
        else:
            pools_data = [api_data]

        for pool_data in pools_data:
            pool = {
                "pool_name": pool_data.get('name', pool_data.get('pool_name', '未知存储池')),
                "pool_id": pool_data.get('resource_pool', pool_data.get('resource_pool', 'UNKNOWN')),
                "total_capacity_gb": pool_data.get('total_capacity', pool_data.get('total_capacity_gb', 0)),
                "used_capacity_gb": pool_data.get('used_capacity', pool_data.get('used_capacity_gb', 0)),
                "available_capacity_gb": pool_data.get('available_capacity', pool_data.get('available_capacity_gb', 0))
            }

            # 计算可用容量（如果API没有提供）
            if pool['available_capacity_gb'] == 0 and pool['total_capacity_gb'] > 0:
                pool['available_capacity_gb'] = pool['total_capacity_gb'] - pool['used_capacity_gb']

            # 添加计算字段
            pool['usage_rate'] = calculate_usage_rate(pool['total_capacity_gb'], pool['used_capacity_gb'])
            # pool['daily_change'] = generate_random_change()
            pool['health_status'] = get_health_status(pool['usage_rate'], {'green': 90, 'yellow': 95})
            pool['has_anomaly'] = pool['health_status'] != "正常"
            pool['measures'] = "无需措施" if not pool['has_anomaly'] else "需要关注并制定调整方案"

            storage_pools.append(pool)

    except Exception as e:
        print(f"解析存储数据失败: {e}")
        return json_response({
            "status": "error",
            "message": f"解析存储数据失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }, 500)

    return json_response({
        "status": "success",
        "timestamp": datetime.now().isoformat(),
        "data": {
            "storage_pools": storage_pools,
            "summary": {
                "total_pools": len(storage_pools),
                "normal_pools": len([p for p in storage_pools if p['health_status'] == "正常"]),
                "warning_pools": len([p for p in storage_pools if p['health_status'] != "正常"])
            }
        }
    })

@app.route('/api/database', methods=['GET'])
def get_database_capacity():
    """获取数据库容量信息"""
    # 调用外部API获取数据库数据
    api_data = call_external_api('/api/database')

    if api_data is None:
        return json_response({
            "status": "error",
            "message": "无法获取数据库容量数据",
            "timestamp": datetime.now().isoformat()
        }, 500)

    # 转换API数据为标准格式
    database_instances = []
    try:
        # 假设API返回的数据结构，根据实际情况调整
        if isinstance(api_data, dict) and 'data' in api_data:
            db_data = api_data['data']
        elif isinstance(api_data, list):
            db_data = api_data
        else:
            db_data = [api_data]

        for db_info in db_data:
            db = {
                "db_name": db_info.get('name', db_info.get('db_name', '未知数据库')),
                "db_id": db_info.get('id', db_info.get('db_id', 'UNKNOWN')),
                "total_capacity_gb": db_info.get('total_capacity', db_info.get('total_capacity_gb', 0)),
                "used_capacity_gb": db_info.get('used_capacity', db_info.get('used_capacity_gb', 0)),
                "available_capacity_gb": db_info.get('available_capacity', db_info.get('available_capacity_gb', 0))
            }

            # 计算可用容量（如果API没有提供）
            if db['available_capacity_gb'] == 0 and db['total_capacity_gb'] > 0:
                db['available_capacity_gb'] = db['total_capacity_gb'] - db['used_capacity_gb']

            # 添加计算字段
            db['usage_rate'] = calculate_usage_rate(db['total_capacity_gb'], db['used_capacity_gb'])
            # db['daily_change'] = generate_random_change()
            db['health_status'] = get_health_status(db['usage_rate'], {'green': 85, 'yellow': 95})
            db['has_anomaly'] = db['health_status'] != "正常"
            db['measures'] = "无需措施" if not db['has_anomaly'] else "需要关注并制定调整方案"

            database_instances.append(db)

    except Exception as e:
        print(f"解析数据库数据失败: {e}")
        return json_response({
            "status": "error",
            "message": f"解析数据库数据失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }, 500)

    return json_response({
        "status": "success",
        "timestamp": datetime.now().isoformat(),
        "data": {
            "database_instances": database_instances,
            "summary": {
                "total_instances": len(database_instances),
                "normal_instances": len([db for db in database_instances if db['health_status'] == "正常"]),
                "warning_instances": len([db for db in database_instances if db['health_status'] != "正常"])
            }
        }
    })

@app.route('/api/container', methods=['GET'])
def get_container_capacity():
    """获取容器(TAP)容量信息"""
    # 调用外部API获取容器数据
    api_data = call_external_api('/api/clusters')

    if api_data is None:
        return json_response({
            "status": "error",
            "message": "无法获取容器容量数据",
            "timestamp": datetime.now().isoformat()
        }, 500)

    # 转换API数据为标准格式
    container_clusters = []
    try:
        # 假设API返回的数据结构，根据实际情况调整
        if isinstance(api_data, dict) and 'data' in api_data:
            clusters_data = api_data['data']
        elif isinstance(api_data, list):
            clusters_data = api_data
        else:
            clusters_data = [api_data]

        for cluster_data in clusters_data:
            cluster = {
                "cluster_name": cluster_data.get('name', cluster_data.get('cluster_name', '未知集群')),
                "cluster_id": cluster_data.get('id', cluster_data.get('cluster_id', 'UNKNOWN')),
                "cpu_total_cores": cluster_data.get('cpu_total', cluster_data.get('cpu_total_cores', 0)),
                "cpu_used_cores": cluster_data.get('cpu_used', cluster_data.get('cpu_used_cores', 0)),
                "memory_total_gb": cluster_data.get('memory_total', cluster_data.get('memory_total_gb', 0)),
                "memory_used_gb": cluster_data.get('memory_used', cluster_data.get('memory_used_gb', 0)),
                "storage_total_gb": cluster_data.get('storage_total', cluster_data.get('storage_total_gb', 0)),
                "storage_used_gb": cluster_data.get('storage_used', cluster_data.get('storage_used_gb', 0))
            }

            # 添加计算字段
            cluster['cpu_usage_rate'] = calculate_usage_rate(cluster['cpu_total_cores'], cluster['cpu_used_cores'])
            cluster['memory_usage_rate'] = calculate_usage_rate(cluster['memory_total_gb'], cluster['memory_used_gb'])
            cluster['storage_usage_rate'] = calculate_usage_rate(cluster['storage_total_gb'], cluster['storage_used_gb'])

            cluster['cpu_available_cores'] = cluster['cpu_total_cores'] - cluster['cpu_used_cores']
            cluster['memory_available_gb'] = cluster['memory_total_gb'] - cluster['memory_used_gb']
            cluster['storage_available_gb'] = cluster['storage_total_gb'] - cluster['storage_used_gb']

            # cluster['cpu_daily_change'] = generate_random_change()
            # cluster['memory_daily_change'] = generate_random_change()
            # cluster['storage_daily_change'] = generate_random_change()

            # 健康状态判断（CPU/内存<80%，存储<90%为正常）
            cpu_health = get_health_status(cluster['cpu_usage_rate'], {'green': 80, 'yellow': 90})
            memory_health = get_health_status(cluster['memory_usage_rate'], {'green': 80, 'yellow': 90})
            storage_health = get_health_status(cluster['storage_usage_rate'], {'green': 90, 'yellow': 95})

            if cpu_health == "警告" or memory_health == "警告" or storage_health == "警告":
                cluster['health_status'] = "警告"
            elif cpu_health == "观察" or memory_health == "观察" or storage_health == "观察":
                cluster['health_status'] = "观察"
            else:
                cluster['health_status'] = "正常"

            cluster['has_anomaly'] = cluster['health_status'] != "正常"
            cluster['measures'] = "无需措施" if not cluster['has_anomaly'] else "需要关注并制定调整方案"

            container_clusters.append(cluster)

    except Exception as e:
        print(f"解析容器数据失败: {e}")
        return json_response({
            "status": "error",
            "message": f"解析容器数据失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }, 500)

    return json_response({
        "status": "success",
        "timestamp": datetime.now().isoformat(),
        "data": {
            "container_clusters": container_clusters,
            "summary": {
                "total_clusters": len(container_clusters),
                "normal_clusters": len([c for c in container_clusters if c['health_status'] == "正常"]),
                "warning_clusters": len([c for c in container_clusters if c['health_status'] != "正常"])
            }
        }
    })

@app.route('/api/virtualization', methods=['GET'])
def get_virtualization_capacity():
    """获取虚拟化容量信息"""
    # 调用外部API获取虚拟化数据
    api_data = call_external_api('/api/vc/ratio')

    if api_data is None:
        return json_response({
            "status": "error",
            "message": "无法获取虚拟化容量数据",
            "timestamp": datetime.now().isoformat()
        }, 500)

    # 转换API数据为标准格式
    vm_clusters = []
    try:
        # 假设API返回的数据结构，根据实际情况调整
        if isinstance(api_data, dict) and 'data' in api_data:
            clusters_data = api_data['data']
        elif isinstance(api_data, list):
            clusters_data = api_data
        else:
            clusters_data = [api_data]

        for cluster_data in clusters_data:
            cluster = {
                "cluster_name": cluster_data.get('vcname', cluster_data.get('cluster_name', '未知虚拟化集群')),
                "host_number": cluster_data.get('host_num',0),
                "vm_number": cluster_data.get('vm_num',0),
                "cpu_total_cores": float(cluster_data.get('cpu_total', cluster_data.get('cpu_total_cores', 0))),
                "cpu_used_cores": float(cluster_data.get('cpu_used', cluster_data.get('cpu_used_cores', 0))),
                "memory_total_gb": float(cluster_data.get('memory_total', cluster_data.get('memory_total_gb', 0))),
                "memory_used_gb": float(cluster_data.get('memory_used', cluster_data.get('memory_used_gb', 0))),
            }

            # 添加计算字段
            cluster['cpu_usage_rate'] = calculate_usage_rate(cluster['cpu_total_cores'], cluster['cpu_used_cores'])
            cluster['memory_usage_rate'] = calculate_usage_rate(cluster['memory_total_gb'], cluster['memory_used_gb'])

            cluster['cpu_available_cores'] = cluster['cpu_total_cores'] - cluster['cpu_used_cores']
            cluster['memory_available_gb'] = cluster['memory_total_gb'] - cluster['memory_used_gb']

            # cluster['cpu_daily_change'] = generate_random_change()
            # cluster['memory_daily_change'] = generate_random_change()

            # 健康状态判断（CPU/内存<75%，存储<90%为正常）
            cpu_health = get_health_status(cluster['cpu_usage_rate'], {'green': 75, 'yellow': 85})
            memory_health = get_health_status(cluster['memory_usage_rate'], {'green': 75, 'yellow': 85})


            if cpu_health == "警告" or memory_health == "警告":
                cluster['health_status'] = "警告"
            elif cpu_health == "观察" or memory_health == "观察":
                cluster['health_status'] = "观察"
            else:
                cluster['health_status'] = "正常"

            cluster['has_anomaly'] = cluster['health_status'] != "正常"
            cluster['measures'] = "无需措施" if not cluster['has_anomaly'] else "需要关注并制定调整方案"

            vm_clusters.append(cluster)

    except Exception as e:
        print(f"解析虚拟化数据失败: {e}")
        return json_response({
            "status": "error",
            "message": f"解析虚拟化数据失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }, 500)

    return json_response({
        "status": "success",
        "timestamp": datetime.now().isoformat(),
        "data": {
            "vm_clusters": vm_clusters,
            "summary": {
                "total_clusters": len(vm_clusters),
                "normal_clusters": len([c for c in vm_clusters if c['health_status'] == "正常"]),
                "warning_clusters": len([c for c in vm_clusters if c['health_status'] != "正常"])
            }
        }
    })

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return json_response({
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "message": "容量监控API服务运行正常"
    })

@app.route('/api/download/<filename>', methods=['GET'])
def download_file(filename):
    """文件下载接口"""
    try:
        # 默认下载目录
        download_dir = '/opt/app/reports'

        # 检查文件是否存在
        file_path = os.path.join(download_dir, filename)
        if not os.path.exists(file_path):
            return json_response({
                'success': False,
                'error': '文件不存在'
            }, 404)

        # 检查文件扩展名安全性
        allowed_extensions = {'.docx', '.pdf', '.xlsx', '.txt'}
        file_ext = os.path.splitext(filename)[1].lower()
        if file_ext not in allowed_extensions:
            return json_response({
                'success': False,
                'error': '不支持的文件类型'
            }, 400)

        # 发送文件
        return send_file(
            file_path,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )

    except Exception as e:
        return json_response({
            'success': False,
            'error': f'文件下载失败: {str(e)}'
        }, 500)



@app.route('/api/get_all_capacity_data', methods=['POST'])
def get_all_capacity_data():
    """获取所有容量数据，供LLM分析使用"""
    try:
        data = request.get_json()
        report_date = data.get('report_date', datetime.now().strftime('%Y-%m-%d'))
        system_name = data.get('system_name', '容量检查报告')

        # 获取所有容量数据
        storage_response = get_storage_capacity()
        database_response = get_database_capacity()
        container_response = get_container_capacity()
        vm_response = get_virtualization_capacity()

        # 提取数据部分
        storage_data = storage_response.get_json()
        database_data = database_response.get_json()
        container_data = container_response.get_json()
        vm_data = vm_response.get_json()

        # 组织数据供LLM分析
        capacity_data = {
            "report_info": {
                "report_date": report_date,
                "system_name": system_name,
                "data_collection_time": datetime.now().isoformat()
            },
            "storage_capacity": storage_data,
            "database_capacity": database_data,
            "container_capacity": container_data,
            "virtualization_capacity": vm_data,
            "summary": {
                "total_storage_pools": len(storage_data.get('pools', [])),
                "total_database_instances": len(database_data.get('instances', [])),
                "total_container_clusters": len(container_data.get('clusters', [])),
                "total_vm_clusters": len(vm_data.get('clusters', []))
            }
        }

        return jsonify({
            'success': True,
            'message': '容量数据获取成功',
            'timestamp': datetime.now().isoformat(),
            'data': capacity_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'容量数据获取失败: {str(e)}',
            'error': str(e)
        }), 500

@app.route('/api/export_word', methods=['POST'])
def export_word():
    """导出Word文档的HTTP接口"""
    try:
        data = request.get_json()

        # 获取参数
        report_content = data.get('report_content', '')
        report_date = datetime.now().strftime('%Y-%m-%d')
        system_name = data.get('system_name', '')
        save_path = data.get('save_path', '/opt/app/reports')

        # 清理系统名称中的特殊字符（与下游导出逻辑保持一致性）
        import re
        system_name = re.sub(r'[^\w\s\-_\u4e00-\u9fff]', '', system_name)

        if not report_content or not report_date or not system_name:
            return json_response({
                'success': False,
                'error': '缺少必要参数：report_content, report_date, system_name'
            }, 400)

        # 目录兜底，确保与导出函数默认目录一致
        if not save_path or str(save_path).strip() == "":
            save_path = '/opt/app/reports'

        # 覆盖写入策略：若目标文件已存在则先删除；否则直接生成
        # 需要与 html_export.export_to_word 内部的命名规则保持一致
        try:
            import os
            os.makedirs(save_path, exist_ok=True)
            safe_name = re.sub(r'[<>:"/\\|?*]', '_', system_name)
            filename = f"{safe_name}_{report_date.replace('-', '')}.docx"
            target_filepath = os.path.join(save_path, filename)
            if os.path.exists(target_filepath):
                # 先删除，确保覆盖
                os.remove(target_filepath)
        except Exception as _:
            # 不中断主流程，继续尝试导出
            pass

        # 导出为Word格式（如文件存在，上面已删除，从而实现覆盖；如不存在则直接创建）
        file_path = export_to_word(report_content, report_date, system_name, save_path)

        # 生成下载URL
        filename = os.path.basename(file_path)
        download_url = f"/api/download/{filename}"

        return json_response({
            'success': True,
            'message': 'Word文档生成成功',
            'file_path': file_path,
            'file_name': filename,
            'file_size': f"{os.path.getsize(file_path) / 1024:.2f} KB",
            'file_type': 'Microsoft Word文档 (.docx)',
            'download_url': download_url,
            'note': '已按请求覆盖同名文档（若已存在）；可通过download_url下载文件'
        })

    except Exception as e:
        return json_response({
            'success': False,
            'error': f'Word导出失败: {str(e)}'
        }, 500)





def extract_query_type_from_classifier_output(classifier_output):
    """从问题分类器的输出中提取查询类型"""
    try:
        # 如果输入已经是简单的查询类型，直接返回
        if classifier_output in ['all', 'storage', 'database', 'container', 'virtualization']:
            return classifier_output

        # 尝试解析JSON格式的输出
        import json
        import re

        # 清理输出，移除可能的markdown格式
        cleaned_output = classifier_output.strip()
        if '```json' in cleaned_output:
            # 提取JSON部分
            json_match = re.search(r'```json\s*(\{.*?\})\s*```', cleaned_output, re.DOTALL)
            if json_match:
                cleaned_output = json_match.group(1)
        elif '```' in cleaned_output:
            # 移除其他markdown标记
            cleaned_output = re.sub(r'```[^`]*```', '', cleaned_output).strip()

        # 尝试解析JSON
        try:
            parsed_data = json.loads(cleaned_output)
            if isinstance(parsed_data, dict) and 'query_type' in parsed_data:
                return parsed_data['query_type']
        except json.JSONDecodeError:
            pass

        # 如果JSON解析失败，尝试从文本中提取查询类型
        text_lower = classifier_output.lower()
        if 'storage' in text_lower:
            return 'storage'
        elif 'database' in text_lower:
            return 'database'
        elif 'container' in text_lower:
            return 'container'
        elif 'virtualization' in text_lower:
            return 'virtualization'
        else:
            return 'all'  # 默认返回全量查询

    except Exception as e:
        print(f"解析问题分类器输出时出错: {str(e)}")
        return 'all'  # 出错时默认返回全量查询

@app.route('/api/get_capacity_data', methods=['POST'])
def get_capacity_data():
    """根据查询类型获取对应的容量数据，供LLM分析使用"""
    try:
        data = request.get_json()
        report_date = data.get('report_date', datetime.now().strftime('%Y-%m-%d'))
        system_name = data.get('system_name', '容量检查报告')
        query_type_raw = data.get('query_type', 'all')

        # 处理问题分类器的JSON输出
        query_type = extract_query_type_from_classifier_output(query_type_raw)

        # 基础报告信息
        report_info = {
            "report_date": report_date,
            "system_name": system_name,
            "query_type": query_type,
            "data_collection_time": datetime.now().isoformat()
        }

        capacity_data = {"report_info": report_info}
        summary = {}

        # 根据查询类型获取对应数据
        # 支持组合查询，如 "storage,virtualization"
        query_types = [t.strip() for t in query_type.split(',')]

        # 验证查询类型
        valid_types = ['all', 'storage', 'database', 'container', 'virtualization']
        for qt in query_types:
            if qt not in valid_types:
                return json_response({
                    'success': False,
                    'message': f'不支持的查询类型: {qt}。支持的类型: {", ".join(valid_types)}'
                }, 400)

        # 如果是all，获取所有数据
        if 'all' in query_types:
            query_types = ['storage', 'database', 'container', 'virtualization']

        # 根据查询类型获取数据
        if 'storage' in query_types:
            storage_response = get_storage_capacity()
            storage_data = storage_response.get_json()
            capacity_data["storage_capacity"] = storage_data
            summary["total_storage_pools"] = len(storage_data.get('pools', []))

        if 'database' in query_types:
            database_response = get_database_capacity()
            database_data = database_response.get_json()
            capacity_data["database_capacity"] = database_data
            summary["total_database_instances"] = len(database_data.get('instances', []))

        if 'container' in query_types:
            container_response = get_container_capacity()
            container_data = container_response.get_json()
            capacity_data["container_capacity"] = container_data
            summary["total_container_clusters"] = len(container_data.get('clusters', []))

        if 'virtualization' in query_types:
            vm_response = get_virtualization_capacity()
            vm_data = vm_response.get_json()
            capacity_data["virtualization_capacity"] = vm_data
            summary["total_vm_clusters"] = len(vm_data.get('clusters', []))

        # 如果没有匹配的查询类型
        if not any(qt in query_types for qt in ['storage', 'database', 'container', 'virtualization']):
            return json_response({
                'success': False,
                'message': f'无效的查询类型组合: {query_type}'
            }, 400)

        capacity_data["summary"] = summary

        # 使用自定义JSON响应函数确保中文字符正确显示
        return json_response(capacity_data)

    except Exception as e:
        return json_response({
            'success': False,
            'message': f'获取容量数据失败: {str(e)}'
        }, 500)



@app.route('/api/cpu_idle', methods=['GET'])
def get_cpu_idle_data():
    # 模拟数据（你可以替换成从数据库或监控系统获取的真实数据）
    data = {
        "rows": [
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.77,
                "time": "2025-09-15 00:00:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.74,
                "time": "2025-09-15 00:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 00:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.1,
                "time": "2025-09-15 00:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 00:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.34,
                "time": "2025-09-15 00:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 00:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.25,
                "time": "2025-09-15 00:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.67,
                "time": "2025-09-15 00:10:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 00:12:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.54,
                "time": "2025-09-15 00:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 00:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.23,
                "time": "2025-09-15 00:14:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.61,
                "time": "2025-09-15 00:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 00:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.32,
                "time": "2025-09-15 00:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.92,
                "time": "2025-09-15 00:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 00:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.61,
                "time": "2025-09-15 00:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 00:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.2,
                "time": "2025-09-15 00:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 00:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.55,
                "time": "2025-09-15 00:26:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.04,
                "time": "2025-09-15 00:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.99,
                "time": "2025-09-15 00:30:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.62,
                "time": "2025-09-15 00:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 00:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.0,
                "time": "2025-09-15 00:34:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.27,
                "time": "2025-09-15 00:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 00:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.31,
                "time": "2025-09-15 00:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.06,
                "time": "2025-09-15 00:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 00:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.89,
                "time": "2025-09-15 00:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.83,
                "time": "2025-09-15 00:44:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 00:46:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.27,
                "time": "2025-09-15 00:46:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 00:48:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.82,
                "time": "2025-09-15 00:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.7,
                "time": "2025-09-15 00:50:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.44,
                "time": "2025-09-15 00:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.97,
                "time": "2025-09-15 00:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 00:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.08,
                "time": "2025-09-15 00:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 00:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.88,
                "time": "2025-09-15 00:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.17,
                "time": "2025-09-15 01:00:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.48,
                "time": "2025-09-15 01:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 01:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.66,
                "time": "2025-09-15 01:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.56,
                "time": "2025-09-15 01:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 01:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.74,
                "time": "2025-09-15 01:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.56,
                "time": "2025-09-15 01:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.85,
                "time": "2025-09-15 01:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 01:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.88,
                "time": "2025-09-15 01:14:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.57,
                "time": "2025-09-15 01:16:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.93,
                "time": "2025-09-15 01:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.54,
                "time": "2025-09-15 01:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 01:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.2,
                "time": "2025-09-15 01:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 01:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.88,
                "time": "2025-09-15 01:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 01:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.69,
                "time": "2025-09-15 01:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 01:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.63,
                "time": "2025-09-15 01:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.69,
                "time": "2025-09-15 01:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 01:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.91,
                "time": "2025-09-15 01:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 01:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.28,
                "time": "2025-09-15 01:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 01:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.63,
                "time": "2025-09-15 01:36:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.65,
                "time": "2025-09-15 01:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.63,
                "time": "2025-09-15 01:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 01:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.45,
                "time": "2025-09-15 01:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.85,
                "time": "2025-09-15 01:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.26,
                "time": "2025-09-15 01:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.06,
                "time": "2025-09-15 01:48:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 01:50:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.96,
                "time": "2025-09-15 01:50:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.21,
                "time": "2025-09-15 01:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.38,
                "time": "2025-09-15 01:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.29,
                "time": "2025-09-15 01:56:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.17,
                "time": "2025-09-15 01:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 83.68,
                "time": "2025-09-15 02:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 02:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.65,
                "time": "2025-09-15 02:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 02:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.96,
                "time": "2025-09-15 02:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.64,
                "time": "2025-09-15 02:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.17,
                "time": "2025-09-15 02:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.79,
                "time": "2025-09-15 02:10:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 02:12:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.89,
                "time": "2025-09-15 02:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 02:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.15,
                "time": "2025-09-15 02:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 02:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.96,
                "time": "2025-09-15 02:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 02:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.04,
                "time": "2025-09-15 02:18:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 02:20:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.56,
                "time": "2025-09-15 02:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 02:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.44,
                "time": "2025-09-15 02:22:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.67,
                "time": "2025-09-15 02:24:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.04,
                "time": "2025-09-15 02:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 02:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.65,
                "time": "2025-09-15 02:28:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 02:30:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.02,
                "time": "2025-09-15 02:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 02:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.83,
                "time": "2025-09-15 02:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 02:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.8,
                "time": "2025-09-15 02:34:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.62,
                "time": "2025-09-15 02:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 02:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.99,
                "time": "2025-09-15 02:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.55,
                "time": "2025-09-15 02:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.4,
                "time": "2025-09-15 02:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.12,
                "time": "2025-09-15 02:44:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 02:46:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.42,
                "time": "2025-09-15 02:46:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 02:48:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.18,
                "time": "2025-09-15 02:48:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 02:50:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.48,
                "time": "2025-09-15 02:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 02:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.63,
                "time": "2025-09-15 02:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.12,
                "time": "2025-09-15 02:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.42,
                "time": "2025-09-15 02:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 02:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.15,
                "time": "2025-09-15 02:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.51,
                "time": "2025-09-15 03:00:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.48,
                "time": "2025-09-15 03:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 03:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.49,
                "time": "2025-09-15 03:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.33,
                "time": "2025-09-15 03:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 03:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.14,
                "time": "2025-09-15 03:08:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 03:10:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.3,
                "time": "2025-09-15 03:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.28,
                "time": "2025-09-15 03:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 03:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.2,
                "time": "2025-09-15 03:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 03:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.67,
                "time": "2025-09-15 03:16:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.23,
                "time": "2025-09-15 03:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.25,
                "time": "2025-09-15 03:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 03:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.56,
                "time": "2025-09-15 03:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 03:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.81,
                "time": "2025-09-15 03:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 03:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.67,
                "time": "2025-09-15 03:26:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.18,
                "time": "2025-09-15 03:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.1,
                "time": "2025-09-15 03:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 03:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.59,
                "time": "2025-09-15 03:32:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.85,
                "time": "2025-09-15 03:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 03:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.54,
                "time": "2025-09-15 03:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 03:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.38,
                "time": "2025-09-15 03:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.49,
                "time": "2025-09-15 03:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 03:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.44,
                "time": "2025-09-15 03:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.1,
                "time": "2025-09-15 03:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.27,
                "time": "2025-09-15 03:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.29,
                "time": "2025-09-15 03:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.05,
                "time": "2025-09-15 03:50:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.69,
                "time": "2025-09-15 03:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 03:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.04,
                "time": "2025-09-15 03:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 03:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.07,
                "time": "2025-09-15 03:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 03:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.6,
                "time": "2025-09-15 03:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.34,
                "time": "2025-09-15 04:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 04:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.54,
                "time": "2025-09-15 04:02:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.23,
                "time": "2025-09-15 04:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.36,
                "time": "2025-09-15 04:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.51,
                "time": "2025-09-15 04:08:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 04:10:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.23,
                "time": "2025-09-15 04:10:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 04:12:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.08,
                "time": "2025-09-15 04:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 04:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.25,
                "time": "2025-09-15 04:14:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.36,
                "time": "2025-09-15 04:16:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.75,
                "time": "2025-09-15 04:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.88,
                "time": "2025-09-15 04:20:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.96,
                "time": "2025-09-15 04:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 04:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.03,
                "time": "2025-09-15 04:24:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.53,
                "time": "2025-09-15 04:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 04:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.74,
                "time": "2025-09-15 04:28:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 04:30:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.44,
                "time": "2025-09-15 04:30:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.55,
                "time": "2025-09-15 04:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 04:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.96,
                "time": "2025-09-15 04:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 04:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.24,
                "time": "2025-09-15 04:36:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.84,
                "time": "2025-09-15 04:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.4,
                "time": "2025-09-15 04:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 04:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 91.03,
                "time": "2025-09-15 04:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.96,
                "time": "2025-09-15 04:44:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 04:46:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.93,
                "time": "2025-09-15 04:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.31,
                "time": "2025-09-15 04:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.97,
                "time": "2025-09-15 04:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 04:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.68,
                "time": "2025-09-15 04:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.06,
                "time": "2025-09-15 04:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.03,
                "time": "2025-09-15 04:56:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.91,
                "time": "2025-09-15 04:58:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.42,
                "time": "2025-09-15 05:00:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.21,
                "time": "2025-09-15 05:00:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.68,
                "time": "2025-09-15 05:02:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.41,
                "time": "2025-09-15 05:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.42,
                "time": "2025-09-15 05:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.07,
                "time": "2025-09-15 05:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.21,
                "time": "2025-09-15 05:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.84,
                "time": "2025-09-15 05:10:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 05:12:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.49,
                "time": "2025-09-15 05:12:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.67,
                "time": "2025-09-15 05:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 05:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.32,
                "time": "2025-09-15 05:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 05:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.12,
                "time": "2025-09-15 05:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.97,
                "time": "2025-09-15 05:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 05:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.61,
                "time": "2025-09-15 05:22:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.67,
                "time": "2025-09-15 05:24:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.93,
                "time": "2025-09-15 05:26:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.15,
                "time": "2025-09-15 05:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.98,
                "time": "2025-09-15 05:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.42,
                "time": "2025-09-15 05:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.06,
                "time": "2025-09-15 05:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 05:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.92,
                "time": "2025-09-15 05:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 05:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.23,
                "time": "2025-09-15 05:36:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.9,
                "time": "2025-09-15 05:38:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 05:40:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.85,
                "time": "2025-09-15 05:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.37,
                "time": "2025-09-15 05:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 05:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.91,
                "time": "2025-09-15 05:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.17,
                "time": "2025-09-15 05:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.47,
                "time": "2025-09-15 05:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.46,
                "time": "2025-09-15 05:50:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.85,
                "time": "2025-09-15 05:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 05:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.93,
                "time": "2025-09-15 05:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 05:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.32,
                "time": "2025-09-15 05:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.42,
                "time": "2025-09-15 05:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.18,
                "time": "2025-09-15 05:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.1,
                "time": "2025-09-15 06:00:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.4,
                "time": "2025-09-15 06:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 06:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.21,
                "time": "2025-09-15 06:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.88,
                "time": "2025-09-15 06:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.42,
                "time": "2025-09-15 06:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.77,
                "time": "2025-09-15 06:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.24,
                "time": "2025-09-15 06:10:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 06:12:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.61,
                "time": "2025-09-15 06:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 06:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.26,
                "time": "2025-09-15 06:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 06:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.52,
                "time": "2025-09-15 06:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 06:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.19,
                "time": "2025-09-15 06:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.47,
                "time": "2025-09-15 06:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 06:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.92,
                "time": "2025-09-15 06:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 06:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.74,
                "time": "2025-09-15 06:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 06:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.79,
                "time": "2025-09-15 06:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 06:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.57,
                "time": "2025-09-15 06:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.51,
                "time": "2025-09-15 06:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 06:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.87,
                "time": "2025-09-15 06:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 06:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.59,
                "time": "2025-09-15 06:34:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.73,
                "time": "2025-09-15 06:36:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.17,
                "time": "2025-09-15 06:38:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 06:40:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.0,
                "time": "2025-09-15 06:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.48,
                "time": "2025-09-15 06:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 06:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.87,
                "time": "2025-09-15 06:44:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 06:46:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.16,
                "time": "2025-09-15 06:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.15,
                "time": "2025-09-15 06:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.03,
                "time": "2025-09-15 06:50:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.82,
                "time": "2025-09-15 06:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.12,
                "time": "2025-09-15 06:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 06:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.76,
                "time": "2025-09-15 06:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 06:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.12,
                "time": "2025-09-15 06:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.07,
                "time": "2025-09-15 07:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.42,
                "time": "2025-09-15 07:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.77,
                "time": "2025-09-15 07:02:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.09,
                "time": "2025-09-15 07:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 07:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.65,
                "time": "2025-09-15 07:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.08,
                "time": "2025-09-15 07:08:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 07:10:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.6,
                "time": "2025-09-15 07:10:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 07:12:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.54,
                "time": "2025-09-15 07:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 07:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.17,
                "time": "2025-09-15 07:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 07:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.62,
                "time": "2025-09-15 07:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 07:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.97,
                "time": "2025-09-15 07:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.36,
                "time": "2025-09-15 07:20:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.72,
                "time": "2025-09-15 07:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 07:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.15,
                "time": "2025-09-15 07:24:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.78,
                "time": "2025-09-15 07:26:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.59,
                "time": "2025-09-15 07:28:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 07:30:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.04,
                "time": "2025-09-15 07:30:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.41,
                "time": "2025-09-15 07:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 07:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.39,
                "time": "2025-09-15 07:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 07:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.32,
                "time": "2025-09-15 07:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 07:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.28,
                "time": "2025-09-15 07:38:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 07:40:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.46,
                "time": "2025-09-15 07:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.54,
                "time": "2025-09-15 07:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 07:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.16,
                "time": "2025-09-15 07:44:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 07:46:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.06,
                "time": "2025-09-15 07:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.84,
                "time": "2025-09-15 07:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.93,
                "time": "2025-09-15 07:50:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.39,
                "time": "2025-09-15 07:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 07:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.18,
                "time": "2025-09-15 07:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.53,
                "time": "2025-09-15 07:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.42,
                "time": "2025-09-15 07:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.91,
                "time": "2025-09-15 07:58:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 08:00:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.35,
                "time": "2025-09-15 08:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 08:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.24,
                "time": "2025-09-15 08:02:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.01,
                "time": "2025-09-15 08:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.22,
                "time": "2025-09-15 08:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 08:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.29,
                "time": "2025-09-15 08:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.15,
                "time": "2025-09-15 08:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.83,
                "time": "2025-09-15 08:12:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.62,
                "time": "2025-09-15 08:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 08:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.94,
                "time": "2025-09-15 08:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 08:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.67,
                "time": "2025-09-15 08:18:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 08:20:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.76,
                "time": "2025-09-15 08:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 08:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.11,
                "time": "2025-09-15 08:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 08:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.18,
                "time": "2025-09-15 08:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 08:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.31,
                "time": "2025-09-15 08:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 08:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.36,
                "time": "2025-09-15 08:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.55,
                "time": "2025-09-15 08:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 08:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.12,
                "time": "2025-09-15 08:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 08:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.12,
                "time": "2025-09-15 08:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 08:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.96,
                "time": "2025-09-15 08:36:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.23,
                "time": "2025-09-15 08:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.23,
                "time": "2025-09-15 08:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.42,
                "time": "2025-09-15 08:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.42,
                "time": "2025-09-15 08:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 08:42:02"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 08:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.58,
                "time": "2025-09-15 08:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.58,
                "time": "2025-09-15 08:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.61,
                "time": "2025-09-15 08:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.61,
                "time": "2025-09-15 08:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.66,
                "time": "2025-09-15 08:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.66,
                "time": "2025-09-15 08:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.64,
                "time": "2025-09-15 08:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 08:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.41,
                "time": "2025-09-15 08:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 08:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.06,
                "time": "2025-09-15 08:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.61,
                "time": "2025-09-15 08:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 08:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.0,
                "time": "2025-09-15 08:58:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 09:00:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.27,
                "time": "2025-09-15 09:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.42,
                "time": "2025-09-15 09:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.76,
                "time": "2025-09-15 09:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 09:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.69,
                "time": "2025-09-15 09:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 09:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.39,
                "time": "2025-09-15 09:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 09:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.81,
                "time": "2025-09-15 09:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.61,
                "time": "2025-09-15 09:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.25,
                "time": "2025-09-15 09:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 09:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.59,
                "time": "2025-09-15 09:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 09:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.25,
                "time": "2025-09-15 09:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 09:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.51,
                "time": "2025-09-15 09:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.5,
                "time": "2025-09-15 09:20:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.06,
                "time": "2025-09-15 09:22:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.25,
                "time": "2025-09-15 09:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 09:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.03,
                "time": "2025-09-15 09:26:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.72,
                "time": "2025-09-15 09:28:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 09:30:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.41,
                "time": "2025-09-15 09:30:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.96,
                "time": "2025-09-15 09:32:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.31,
                "time": "2025-09-15 09:34:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.01,
                "time": "2025-09-15 09:36:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.82,
                "time": "2025-09-15 09:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.04,
                "time": "2025-09-15 09:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.42,
                "time": "2025-09-15 09:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.2,
                "time": "2025-09-15 09:44:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 09:46:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.12,
                "time": "2025-09-15 09:46:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 09:48:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.88,
                "time": "2025-09-15 09:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.13,
                "time": "2025-09-15 09:50:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.83,
                "time": "2025-09-15 09:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 09:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.37,
                "time": "2025-09-15 09:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 09:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.54,
                "time": "2025-09-15 09:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 09:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.45,
                "time": "2025-09-15 09:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.1,
                "time": "2025-09-15 10:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 10:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.9,
                "time": "2025-09-15 10:02:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.52,
                "time": "2025-09-15 10:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 10:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.45,
                "time": "2025-09-15 10:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.15,
                "time": "2025-09-15 10:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.42,
                "time": "2025-09-15 10:10:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 10:12:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.65,
                "time": "2025-09-15 10:12:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.46,
                "time": "2025-09-15 10:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 10:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.49,
                "time": "2025-09-15 10:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 10:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.95,
                "time": "2025-09-15 10:18:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 10:20:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.31,
                "time": "2025-09-15 10:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 10:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.91,
                "time": "2025-09-15 10:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 10:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.9,
                "time": "2025-09-15 10:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 10:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.18,
                "time": "2025-09-15 10:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 10:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.53,
                "time": "2025-09-15 10:28:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 10:30:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.95,
                "time": "2025-09-15 10:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 10:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.54,
                "time": "2025-09-15 10:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 10:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.84,
                "time": "2025-09-15 10:34:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.31,
                "time": "2025-09-15 10:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 10:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.5,
                "time": "2025-09-15 10:38:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 10:40:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.38,
                "time": "2025-09-15 10:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 10:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.21,
                "time": "2025-09-15 10:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 10:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.19,
                "time": "2025-09-15 10:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.3,
                "time": "2025-09-15 10:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.3,
                "time": "2025-09-15 10:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.37,
                "time": "2025-09-15 10:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 10:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.75,
                "time": "2025-09-15 10:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.53,
                "time": "2025-09-15 10:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.45,
                "time": "2025-09-15 10:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 10:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.76,
                "time": "2025-09-15 10:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.78,
                "time": "2025-09-15 11:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.42,
                "time": "2025-09-15 11:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.14,
                "time": "2025-09-15 11:02:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.16,
                "time": "2025-09-15 11:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.64,
                "time": "2025-09-15 11:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.37,
                "time": "2025-09-15 11:08:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 11:10:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.69,
                "time": "2025-09-15 11:10:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 11:12:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.91,
                "time": "2025-09-15 11:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 11:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.82,
                "time": "2025-09-15 11:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 11:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.21,
                "time": "2025-09-15 11:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 11:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.98,
                "time": "2025-09-15 11:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.57,
                "time": "2025-09-15 11:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 11:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.23,
                "time": "2025-09-15 11:22:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.8,
                "time": "2025-09-15 11:24:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.74,
                "time": "2025-09-15 11:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 11:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.35,
                "time": "2025-09-15 11:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.68,
                "time": "2025-09-15 11:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 11:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.94,
                "time": "2025-09-15 11:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 11:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.56,
                "time": "2025-09-15 11:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 11:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.69,
                "time": "2025-09-15 11:36:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.27,
                "time": "2025-09-15 11:38:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 11:40:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.16,
                "time": "2025-09-15 11:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 11:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.9,
                "time": "2025-09-15 11:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 11:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.56,
                "time": "2025-09-15 11:44:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 11:46:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.91,
                "time": "2025-09-15 11:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.32,
                "time": "2025-09-15 11:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.05,
                "time": "2025-09-15 11:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 11:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.41,
                "time": "2025-09-15 11:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.43,
                "time": "2025-09-15 11:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.5,
                "time": "2025-09-15 11:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 11:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.9,
                "time": "2025-09-15 11:58:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 12:00:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.1,
                "time": "2025-09-15 12:00:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.6,
                "time": "2025-09-15 12:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 12:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.25,
                "time": "2025-09-15 12:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 12:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.3,
                "time": "2025-09-15 12:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 12:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.24,
                "time": "2025-09-15 12:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.08,
                "time": "2025-09-15 12:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.86,
                "time": "2025-09-15 12:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.48,
                "time": "2025-09-15 12:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.7,
                "time": "2025-09-15 12:14:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.47,
                "time": "2025-09-15 12:16:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.9,
                "time": "2025-09-15 12:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.6,
                "time": "2025-09-15 12:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 12:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.27,
                "time": "2025-09-15 12:22:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.89,
                "time": "2025-09-15 12:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 12:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.38,
                "time": "2025-09-15 12:26:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.65,
                "time": "2025-09-15 12:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.48,
                "time": "2025-09-15 12:30:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.25,
                "time": "2025-09-15 12:32:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.28,
                "time": "2025-09-15 12:34:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.14,
                "time": "2025-09-15 12:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 12:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.2,
                "time": "2025-09-15 12:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.44,
                "time": "2025-09-15 12:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.85,
                "time": "2025-09-15 12:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 12:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.28,
                "time": "2025-09-15 12:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.25,
                "time": "2025-09-15 12:46:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 12:48:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.52,
                "time": "2025-09-15 12:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.42,
                "time": "2025-09-15 12:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.42,
                "time": "2025-09-15 12:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.27,
                "time": "2025-09-15 12:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 12:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.16,
                "time": "2025-09-15 12:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 12:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.53,
                "time": "2025-09-15 12:56:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.63,
                "time": "2025-09-15 12:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.5,
                "time": "2025-09-15 13:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.44,
                "time": "2025-09-15 13:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.6,
                "time": "2025-09-15 13:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 13:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.47,
                "time": "2025-09-15 13:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.42,
                "time": "2025-09-15 13:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.9,
                "time": "2025-09-15 13:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.1,
                "time": "2025-09-15 13:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.1,
                "time": "2025-09-15 13:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.34,
                "time": "2025-09-15 13:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.34,
                "time": "2025-09-15 13:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.87,
                "time": "2025-09-15 13:12:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.87,
                "time": "2025-09-15 13:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 13:14:02"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 13:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.34,
                "time": "2025-09-15 13:14:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.34,
                "time": "2025-09-15 13:14:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.72,
                "time": "2025-09-15 13:16:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.72,
                "time": "2025-09-15 13:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 13:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.48,
                "time": "2025-09-15 13:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.47,
                "time": "2025-09-15 13:20:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.76,
                "time": "2025-09-15 13:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.45,
                "time": "2025-09-15 13:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.57,
                "time": "2025-09-15 13:24:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.5,
                "time": "2025-09-15 13:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 13:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.08,
                "time": "2025-09-15 13:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.09,
                "time": "2025-09-15 13:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 13:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.74,
                "time": "2025-09-15 13:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 13:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.37,
                "time": "2025-09-15 13:34:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.59,
                "time": "2025-09-15 13:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 13:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.09,
                "time": "2025-09-15 13:38:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 13:40:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.86,
                "time": "2025-09-15 13:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.43,
                "time": "2025-09-15 13:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.47,
                "time": "2025-09-15 13:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 13:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.74,
                "time": "2025-09-15 13:44:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.46,
                "time": "2025-09-15 13:46:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.92,
                "time": "2025-09-15 13:46:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 13:48:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.9,
                "time": "2025-09-15 13:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.03,
                "time": "2025-09-15 13:50:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.52,
                "time": "2025-09-15 13:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.9,
                "time": "2025-09-15 13:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 13:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.99,
                "time": "2025-09-15 13:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 13:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.29,
                "time": "2025-09-15 13:58:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 14:00:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.77,
                "time": "2025-09-15 14:00:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.02,
                "time": "2025-09-15 14:02:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.58,
                "time": "2025-09-15 14:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.47,
                "time": "2025-09-15 14:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.42,
                "time": "2025-09-15 14:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.56,
                "time": "2025-09-15 14:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.68,
                "time": "2025-09-15 14:10:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.61,
                "time": "2025-09-15 14:12:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.27,
                "time": "2025-09-15 14:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.61,
                "time": "2025-09-15 14:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.92,
                "time": "2025-09-15 14:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 14:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.99,
                "time": "2025-09-15 14:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.64,
                "time": "2025-09-15 14:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.68,
                "time": "2025-09-15 14:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.61,
                "time": "2025-09-15 14:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.65,
                "time": "2025-09-15 14:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.92,
                "time": "2025-09-15 14:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 14:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.61,
                "time": "2025-09-15 14:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 14:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.52,
                "time": "2025-09-15 14:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.59,
                "time": "2025-09-15 14:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.71,
                "time": "2025-09-15 14:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.02,
                "time": "2025-09-15 14:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 14:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.39,
                "time": "2025-09-15 14:32:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.77,
                "time": "2025-09-15 14:34:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.36,
                "time": "2025-09-15 14:36:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.09,
                "time": "2025-09-15 14:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.4,
                "time": "2025-09-15 14:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.94,
                "time": "2025-09-15 14:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.16,
                "time": "2025-09-15 14:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.46,
                "time": "2025-09-15 14:46:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.63,
                "time": "2025-09-15 14:48:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.64,
                "time": "2025-09-15 14:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.87,
                "time": "2025-09-15 14:50:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.8,
                "time": "2025-09-15 14:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.33,
                "time": "2025-09-15 14:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 14:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.36,
                "time": "2025-09-15 14:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.6,
                "time": "2025-09-15 14:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.79,
                "time": "2025-09-15 14:58:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 15:00:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.8,
                "time": "2025-09-15 15:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.49,
                "time": "2025-09-15 15:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.64,
                "time": "2025-09-15 15:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.6,
                "time": "2025-09-15 15:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.35,
                "time": "2025-09-15 15:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.08,
                "time": "2025-09-15 15:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.02,
                "time": "2025-09-15 15:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.91,
                "time": "2025-09-15 15:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.31,
                "time": "2025-09-15 15:12:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.3,
                "time": "2025-09-15 15:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 15:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.02,
                "time": "2025-09-15 15:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 15:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.71,
                "time": "2025-09-15 15:18:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 15:20:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.06,
                "time": "2025-09-15 15:20:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.69,
                "time": "2025-09-15 15:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 15:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.52,
                "time": "2025-09-15 15:24:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.05,
                "time": "2025-09-15 15:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 15:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.57,
                "time": "2025-09-15 15:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.03,
                "time": "2025-09-15 15:30:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.74,
                "time": "2025-09-15 15:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 15:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.14,
                "time": "2025-09-15 15:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 15:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.2,
                "time": "2025-09-15 15:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 15:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.77,
                "time": "2025-09-15 15:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.69,
                "time": "2025-09-15 15:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.64,
                "time": "2025-09-15 15:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.02,
                "time": "2025-09-15 15:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.3,
                "time": "2025-09-15 15:46:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 15:48:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.64,
                "time": "2025-09-15 15:48:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 15:50:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.18,
                "time": "2025-09-15 15:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 15:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.15,
                "time": "2025-09-15 15:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 15:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.86,
                "time": "2025-09-15 15:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.93,
                "time": "2025-09-15 15:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 15:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.09,
                "time": "2025-09-15 15:58:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 16:00:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.86,
                "time": "2025-09-15 16:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 16:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.08,
                "time": "2025-09-15 16:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.62,
                "time": "2025-09-15 16:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.74,
                "time": "2025-09-15 16:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 16:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.94,
                "time": "2025-09-15 16:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 16:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.82,
                "time": "2025-09-15 16:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.24,
                "time": "2025-09-15 16:10:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 16:12:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.08,
                "time": "2025-09-15 16:12:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 86.91,
                "time": "2025-09-15 16:14:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.66,
                "time": "2025-09-15 16:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.66,
                "time": "2025-09-15 16:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.35,
                "time": "2025-09-15 16:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.0,
                "time": "2025-09-15 16:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.62,
                "time": "2025-09-15 16:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.53,
                "time": "2025-09-15 16:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.59,
                "time": "2025-09-15 16:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.87,
                "time": "2025-09-15 16:24:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.23,
                "time": "2025-09-15 16:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.66,
                "time": "2025-09-15 16:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.38,
                "time": "2025-09-15 16:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.42,
                "time": "2025-09-15 16:30:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.4,
                "time": "2025-09-15 16:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.63,
                "time": "2025-09-15 16:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.24,
                "time": "2025-09-15 16:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.7,
                "time": "2025-09-15 16:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.78,
                "time": "2025-09-15 16:36:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.55,
                "time": "2025-09-15 16:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.55,
                "time": "2025-09-15 16:38:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 16:40:02"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 16:40:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.12,
                "time": "2025-09-15 16:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.12,
                "time": "2025-09-15 16:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 16:42:02"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 16:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.09,
                "time": "2025-09-15 16:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 16:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.5,
                "time": "2025-09-15 16:44:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 16:46:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.04,
                "time": "2025-09-15 16:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.17,
                "time": "2025-09-15 16:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.34,
                "time": "2025-09-15 16:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 16:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.05,
                "time": "2025-09-15 16:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.92,
                "time": "2025-09-15 16:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 16:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.15,
                "time": "2025-09-15 16:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 16:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.25,
                "time": "2025-09-15 16:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.75,
                "time": "2025-09-15 17:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 17:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.55,
                "time": "2025-09-15 17:02:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.69,
                "time": "2025-09-15 17:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 17:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.39,
                "time": "2025-09-15 17:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.62,
                "time": "2025-09-15 17:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.87,
                "time": "2025-09-15 17:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.45,
                "time": "2025-09-15 17:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.5,
                "time": "2025-09-15 17:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 17:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.18,
                "time": "2025-09-15 17:14:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.49,
                "time": "2025-09-15 17:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.65,
                "time": "2025-09-15 17:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.96,
                "time": "2025-09-15 17:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.2,
                "time": "2025-09-15 17:20:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.05,
                "time": "2025-09-15 17:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.62,
                "time": "2025-09-15 17:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.35,
                "time": "2025-09-15 17:24:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.81,
                "time": "2025-09-15 17:26:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.22,
                "time": "2025-09-15 17:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.9,
                "time": "2025-09-15 17:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.64,
                "time": "2025-09-15 17:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.69,
                "time": "2025-09-15 17:32:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.42,
                "time": "2025-09-15 17:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 17:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.97,
                "time": "2025-09-15 17:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.59,
                "time": "2025-09-15 17:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.7,
                "time": "2025-09-15 17:38:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 17:40:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.29,
                "time": "2025-09-15 17:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 17:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.0,
                "time": "2025-09-15 17:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.63,
                "time": "2025-09-15 17:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.81,
                "time": "2025-09-15 17:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.39,
                "time": "2025-09-15 17:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.2,
                "time": "2025-09-15 17:48:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 17:50:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.01,
                "time": "2025-09-15 17:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.63,
                "time": "2025-09-15 17:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.19,
                "time": "2025-09-15 17:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.61,
                "time": "2025-09-15 17:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.35,
                "time": "2025-09-15 17:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 17:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.15,
                "time": "2025-09-15 17:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 17:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.47,
                "time": "2025-09-15 17:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.82,
                "time": "2025-09-15 18:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.64,
                "time": "2025-09-15 18:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.0,
                "time": "2025-09-15 18:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 18:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.35,
                "time": "2025-09-15 18:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.82,
                "time": "2025-09-15 18:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.9,
                "time": "2025-09-15 18:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.81,
                "time": "2025-09-15 18:10:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 18:12:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.07,
                "time": "2025-09-15 18:12:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.18,
                "time": "2025-09-15 18:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 18:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.26,
                "time": "2025-09-15 18:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.59,
                "time": "2025-09-15 18:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.55,
                "time": "2025-09-15 18:18:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 18:20:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.93,
                "time": "2025-09-15 18:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.63,
                "time": "2025-09-15 18:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.75,
                "time": "2025-09-15 18:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.68,
                "time": "2025-09-15 18:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.76,
                "time": "2025-09-15 18:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 18:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.07,
                "time": "2025-09-15 18:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.68,
                "time": "2025-09-15 18:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.9,
                "time": "2025-09-15 18:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.05,
                "time": "2025-09-15 18:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.61,
                "time": "2025-09-15 18:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.84,
                "time": "2025-09-15 18:32:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.42,
                "time": "2025-09-15 18:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 18:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.91,
                "time": "2025-09-15 18:36:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.06,
                "time": "2025-09-15 18:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.1,
                "time": "2025-09-15 18:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.46,
                "time": "2025-09-15 18:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 18:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.13,
                "time": "2025-09-15 18:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.73,
                "time": "2025-09-15 18:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.24,
                "time": "2025-09-15 18:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.43,
                "time": "2025-09-15 18:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 18:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.42,
                "time": "2025-09-15 18:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 18:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.05,
                "time": "2025-09-15 18:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.61,
                "time": "2025-09-15 18:56:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.35,
                "time": "2025-09-15 18:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.86,
                "time": "2025-09-15 19:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 19:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.65,
                "time": "2025-09-15 19:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 19:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.65,
                "time": "2025-09-15 19:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 19:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.87,
                "time": "2025-09-15 19:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.6,
                "time": "2025-09-15 19:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.5,
                "time": "2025-09-15 19:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.34,
                "time": "2025-09-15 19:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.44,
                "time": "2025-09-15 19:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 19:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.34,
                "time": "2025-09-15 19:14:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.78,
                "time": "2025-09-15 19:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.6,
                "time": "2025-09-15 19:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.6,
                "time": "2025-09-15 19:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.55,
                "time": "2025-09-15 19:20:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.41,
                "time": "2025-09-15 19:22:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.92,
                "time": "2025-09-15 19:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 19:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.85,
                "time": "2025-09-15 19:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.6,
                "time": "2025-09-15 19:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.97,
                "time": "2025-09-15 19:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.1,
                "time": "2025-09-15 19:30:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.86,
                "time": "2025-09-15 19:32:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.14,
                "time": "2025-09-15 19:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 19:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.9,
                "time": "2025-09-15 19:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 19:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.67,
                "time": "2025-09-15 19:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.06,
                "time": "2025-09-15 19:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.8,
                "time": "2025-09-15 19:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 19:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.76,
                "time": "2025-09-15 19:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.86,
                "time": "2025-09-15 19:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.72,
                "time": "2025-09-15 19:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.83,
                "time": "2025-09-15 19:50:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.38,
                "time": "2025-09-15 19:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 19:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.54,
                "time": "2025-09-15 19:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.87,
                "time": "2025-09-15 19:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 19:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.59,
                "time": "2025-09-15 19:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.0,
                "time": "2025-09-15 20:00:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.4,
                "time": "2025-09-15 20:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.5,
                "time": "2025-09-15 20:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.27,
                "time": "2025-09-15 20:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.97,
                "time": "2025-09-15 20:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.57,
                "time": "2025-09-15 20:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.51,
                "time": "2025-09-15 20:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.45,
                "time": "2025-09-15 20:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 20:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.48,
                "time": "2025-09-15 20:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.6,
                "time": "2025-09-15 20:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.37,
                "time": "2025-09-15 20:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.62,
                "time": "2025-09-15 20:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.7,
                "time": "2025-09-15 20:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.28,
                "time": "2025-09-15 20:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 20:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.55,
                "time": "2025-09-15 20:22:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 91.1,
                "time": "2025-09-15 20:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.59,
                "time": "2025-09-15 20:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.1,
                "time": "2025-09-15 20:26:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 20:28:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.38,
                "time": "2025-09-15 20:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.38,
                "time": "2025-09-15 20:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 20:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.71,
                "time": "2025-09-15 20:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 20:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.88,
                "time": "2025-09-15 20:34:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.23,
                "time": "2025-09-15 20:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.62,
                "time": "2025-09-15 20:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.13,
                "time": "2025-09-15 20:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.37,
                "time": "2025-09-15 20:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.62,
                "time": "2025-09-15 20:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.49,
                "time": "2025-09-15 20:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.54,
                "time": "2025-09-15 20:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.29,
                "time": "2025-09-15 20:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.95,
                "time": "2025-09-15 20:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.23,
                "time": "2025-09-15 20:50:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.09,
                "time": "2025-09-15 20:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 91.02,
                "time": "2025-09-15 20:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 20:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.24,
                "time": "2025-09-15 20:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.64,
                "time": "2025-09-15 20:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.54,
                "time": "2025-09-15 20:58:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 21:00:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.06,
                "time": "2025-09-15 21:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 21:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.23,
                "time": "2025-09-15 21:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 21:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.55,
                "time": "2025-09-15 21:04:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 21:06:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.81,
                "time": "2025-09-15 21:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 21:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.43,
                "time": "2025-09-15 21:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.4,
                "time": "2025-09-15 21:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.4,
                "time": "2025-09-15 21:12:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.96,
                "time": "2025-09-15 21:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 21:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.09,
                "time": "2025-09-15 21:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.62,
                "time": "2025-09-15 21:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.92,
                "time": "2025-09-15 21:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.45,
                "time": "2025-09-15 21:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 21:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.1,
                "time": "2025-09-15 21:22:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.62,
                "time": "2025-09-15 21:24:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.29,
                "time": "2025-09-15 21:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.55,
                "time": "2025-09-15 21:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.35,
                "time": "2025-09-15 21:26:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.22,
                "time": "2025-09-15 21:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.48,
                "time": "2025-09-15 21:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.59,
                "time": "2025-09-15 21:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.42,
                "time": "2025-09-15 21:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.59,
                "time": "2025-09-15 21:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.41,
                "time": "2025-09-15 21:34:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.72,
                "time": "2025-09-15 21:36:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.14,
                "time": "2025-09-15 21:38:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.59,
                "time": "2025-09-15 21:40:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.6,
                "time": "2025-09-15 21:42:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.64,
                "time": "2025-09-15 21:42:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 21:44:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.43,
                "time": "2025-09-15 21:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.3,
                "time": "2025-09-15 21:46:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.51,
                "time": "2025-09-15 21:48:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.06,
                "time": "2025-09-15 21:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.63,
                "time": "2025-09-15 21:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 21:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.18,
                "time": "2025-09-15 21:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 21:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.49,
                "time": "2025-09-15 21:54:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 21:56:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.27,
                "time": "2025-09-15 21:56:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.07,
                "time": "2025-09-15 21:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.96,
                "time": "2025-09-15 22:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 22:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.0,
                "time": "2025-09-15 22:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.64,
                "time": "2025-09-15 22:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.9,
                "time": "2025-09-15 22:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.44,
                "time": "2025-09-15 22:06:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.51,
                "time": "2025-09-15 22:08:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.46,
                "time": "2025-09-15 22:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.32,
                "time": "2025-09-15 22:12:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.73,
                "time": "2025-09-15 22:14:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.81,
                "time": "2025-09-15 22:16:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 22:18:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.3,
                "time": "2025-09-15 22:18:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.99,
                "time": "2025-09-15 22:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.6,
                "time": "2025-09-15 22:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.83,
                "time": "2025-09-15 22:22:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 91.13,
                "time": "2025-09-15 22:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.64,
                "time": "2025-09-15 22:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.59,
                "time": "2025-09-15 22:26:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.77,
                "time": "2025-09-15 22:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.32,
                "time": "2025-09-15 22:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.6,
                "time": "2025-09-15 22:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.3,
                "time": "2025-09-15 22:32:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 22:34:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.2,
                "time": "2025-09-15 22:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 22:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.4,
                "time": "2025-09-15 22:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 22:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.8,
                "time": "2025-09-15 22:38:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.56,
                "time": "2025-09-15 22:40:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.56,
                "time": "2025-09-15 22:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.12,
                "time": "2025-09-15 22:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.72,
                "time": "2025-09-15 22:44:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 22:46:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.75,
                "time": "2025-09-15 22:46:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.22,
                "time": "2025-09-15 22:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.91,
                "time": "2025-09-15 22:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.58,
                "time": "2025-09-15 22:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.88,
                "time": "2025-09-15 22:52:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.62,
                "time": "2025-09-15 22:54:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.71,
                "time": "2025-09-15 22:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.23,
                "time": "2025-09-15 22:56:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.92,
                "time": "2025-09-15 22:58:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.82,
                "time": "2025-09-15 23:00:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 23:02:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.62,
                "time": "2025-09-15 23:02:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.54,
                "time": "2025-09-15 23:04:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.6,
                "time": "2025-09-15 23:04:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.78,
                "time": "2025-09-15 23:06:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.6,
                "time": "2025-09-15 23:08:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.49,
                "time": "2025-09-15 23:08:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.66,
                "time": "2025-09-15 23:10:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.4,
                "time": "2025-09-15 23:10:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.28,
                "time": "2025-09-15 23:12:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 23:14:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.26,
                "time": "2025-09-15 23:14:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 23:16:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.74,
                "time": "2025-09-15 23:16:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.91,
                "time": "2025-09-15 23:18:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.6,
                "time": "2025-09-15 23:20:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 87.79,
                "time": "2025-09-15 23:20:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.59,
                "time": "2025-09-15 23:22:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.21,
                "time": "2025-09-15 23:22:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.0,
                "time": "2025-09-15 23:24:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.52,
                "time": "2025-09-15 23:26:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.9,
                "time": "2025-09-15 23:26:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.25,
                "time": "2025-09-15 23:28:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.43,
                "time": "2025-09-15 23:30:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.59,
                "time": "2025-09-15 23:32:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.46,
                "time": "2025-09-15 23:32:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.95,
                "time": "2025-09-15 23:34:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.63,
                "time": "2025-09-15 23:36:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.76,
                "time": "2025-09-15 23:36:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.63,
                "time": "2025-09-15 23:38:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.29,
                "time": "2025-09-15 23:38:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.64,
                "time": "2025-09-15 23:40:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.42,
                "time": "2025-09-15 23:40:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.24,
                "time": "2025-09-15 23:42:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.14,
                "time": "2025-09-15 23:44:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 90.71,
                "time": "2025-09-15 23:46:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.57,
                "time": "2025-09-15 23:48:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.85,
                "time": "2025-09-15 23:48:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.76,
                "time": "2025-09-15 23:50:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.59,
                "time": "2025-09-15 23:52:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.94,
                "time": "2025-09-15 23:52:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 89.58,
                "time": "2025-09-15 23:54:15"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.6,
                "time": "2025-09-15 23:56:15"
            },
            {
                "metric": "os.mem.used_ratio",
                "objectip": "**************",
                "value": 13.53,
                "time": "2025-09-15 23:58:02"
            },
            {
                "metric": "os.cpu.idle_ratio",
                "objectip": "**************",
                "value": 88.89,
                "time": "2025-09-15 23:58:15"
            }
        ],
        "row_count": 1108
        }
    
    return jsonify(data)

# 定义指标映射和显示名称
METRIC_MAP = {
    "os.cpu.idle_ratio": "CPU空闲率 (%)",
    "os.mem.used_ratio": "内存使用率 (%)",
    "os.disk.used_ratio": "磁盘使用率 (%)"
}

def parse_time(time_str):
    """解析 'YYYY-MM-DD HH:MM:SS' 格式为 datetime 对象"""
    return datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")

def format_time_only(dt):
    """提取 'HH:MM:SS' 字符串"""
    return dt.strftime("%H:%M:%S")

def find_closest_data(target_time, data_list, tolerance_minutes=2):
    """
    在数据列表中，查找与 target_time 最接近的数据
    优先选择秒数为02的数据（如果存在），且在 tolerance_minutes 分钟内
    返回 (value, time_str, is_exact)
    """
    if not data_list:
        return None, None, False

    candidates = []
    target_dt = parse_time(target_time)
    lower_bound = target_dt - timedelta(minutes=tolerance_minutes)
    upper_bound = target_dt + timedelta(minutes=tolerance_minutes)

    for data_time_str, value in data_list:
        data_dt = parse_time(data_time_str)
        if lower_bound <= data_dt <= upper_bound:
            # 优先级：秒数是否为02
            priority = 0 if data_dt.second == 2 else 1
            # 时间差绝对值
            time_diff = abs((data_dt - target_dt).total_seconds())
            candidates.append((priority, time_diff, value, data_time_str))

    if not candidates:
        return None, None, False

    # 排序：先按优先级（02秒优先），再按时间差
    candidates.sort(key=lambda x: (x[0], x[1]))
    best = candidates[0]
    # 判断是否为“精确匹配”：秒数为02 且 时间差在2分钟内
    is_exact = (parse_time(best[3]).second == 2) and (abs(parse_time(best[3]) - target_dt) <= timedelta(minutes=2))
    return best[2], format_time_only(parse_time(best[3])), is_exact

def generate_report_from_data(data, report_id):
    """
    根据输入的 JSON 数据生成性能报告文本文件。
    
    Args:
        data (dict): 包含 'rows' 键的字典，结构如示例。
        report_id (str): 报告唯一标识，用于生成文件名。
    
    Returns:
        str: 生成的报告文件的下载 URL。
    """
    # 确保输出目录存在
    output_dir = "/opt/app/reports/"
    os.makedirs(output_dir, exist_ok=True)
    
    # 定义输出文件路径
    output_filename = f"report_{report_id}.txt"
    output_filepath = os.path.join(output_dir, output_filename)

    # 按 (ip, date) 分组，并记录每个组出现的指标
    groups = defaultdict(lambda: defaultdict(list))  # { (ip, date): {metric: [(time, value), ...]} }

    for row in data["rows"]:
        ip = row["objectip"]
        full_time = row["time"]
        date_str = full_time.split()[0]  # "2025-09-15"
        metric = row["metric"]
        value = row["value"]

        key = (ip, date_str)
        groups[key][metric].append((full_time, value))

    # 开始写入报告
    with open(output_filepath, 'w', encoding='utf-8') as f:
        first_group = True
        for (ip, date), metrics_data in sorted(groups.items()):
            if not first_group:
                f.write("\n\n")  # 组间空行分隔
            first_group = False

            # 写标题
            f.write(f"服务器性能报告 (IP: {ip} | 日期: {date})\n\n")

            # 动态生成表头
            # CPU 数据作为主时间轴
            cpu_data = metrics_data.get("os.cpu.idle_ratio", [])
            if not cpu_data:
                f.write("无CPU数据，无法生成报告。\n")
                continue

            # 获取本组出现的所有指标（按固定顺序：CPU, 内存, 磁盘）
            group_metrics = [m for m in METRIC_MAP.keys() if m in metrics_data]
            # 表头
            header_cols = ["时间点"] + [METRIC_MAP[m] for m in group_metrics]
            separator_cols = ["-------"] + ["-" * len(METRIC_MAP[m]) for m in group_metrics]

            f.write(" | ".join(header_cols) + "\n")
            f.write("|".join(separator_cols) + "\n")

            # 按时间排序 CPU 数据
            cpu_list = sorted(cpu_data, key=lambda x: parse_time(x[0]))

            for full_time, cpu_value in cpu_list:
                time_only = format_time_only(parse_time(full_time))
                row_values = [time_only, f"{cpu_value:.2f}"]  # 第一列是时间，第二列是CPU

                # 为其他每个指标查找匹配数据
                for metric in group_metrics[1:]:  # 跳过CPU，它已经是主轴
                    data_list = metrics_data[metric]
                    value, matched_time, is_exact = find_closest_data(full_time, data_list)

                    if value is None:
                        display_value = "-"
                    else:
                        display_value = f"{value:.2f}"
                        if matched_time:
                            display_value += f" ({matched_time})"
                        if not is_exact:
                            display_value += " *注：使用最近数据"

                    row_values.append(display_value)

                f.write(" | ".join(row_values) + "\n")

        # 写脚注
        f.write("\n*注：报告中“-”表示该时间点无精确匹配数据，可使用邻近时间点数据作为参考。\n")

    # 假设你的Web服务根地址是 http://your-server.com
    # 并且你有一个路由 /download_report/<filename> 来提供文件下载
    download_url = f"http://172.30.224.1:5002/api/download/{output_filename}"
    
    return download_url

@app.route('/api/generate_report', methods=['POST'])
def generate_report():
    data = request.get_json()  # 获取 POST 的 JSON 数据
    report_id = data.get('report_id', 'default')  # 从查询参数或请求体获取 report_id
    metric = data.get('metric', None)
    try:
        download_url = generate_report_from_data(metric, report_id)
        return jsonify({"success": True, "download_url": download_url}), 200
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500
    
if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5002, debug=True)
